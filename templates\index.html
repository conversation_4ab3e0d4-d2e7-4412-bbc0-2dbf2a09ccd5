<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini 绘本生成器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <div class="container">
        <h1>Gemini 绘本生成器</h1>

        <div class="form-container">
            <form id="promptForm">
                <div class="model-selection">
                    <label>选择模型:</label>
                    <div class="radio-group">
                        <label>
                            <input type="radio" name="model" value="gemini-2.0-flash" checked>
                            gemini-2.0-flash
                        </label>
                        <label>
                            <input type="radio" name="model" value="gemini-2.0-flash-exp-image-generation">
                            gemini-2.0-flash-exp-image-generation
                        </label>
                    </div>
                </div>
                <div>
                    <label for="prompt">输入提示词:</label>
                    <div id="prompt" name="prompt" contenteditable="true" class="rich-text-editor" style="min-height:120px;border:1px solid #ccc;padding:8px;border-radius:4px;background:#fff;outline:none;" required>{{ default_prompt|safe }}</div>
                </div>

                <div class="button-container">
                    <button type="submit" id="generateBtn">生成绘本</button>
                    <button type="button" id="testApiBtn" class="test-btn">测试API连接</button>
                    <button type="button" id="exportBtn" class="export-btn">导出故事</button>
                </div>

                <div id="apiInfo">使用API密钥: {{ api_key }}</div>
            </form>

            <div id="statusMessage" class="status"></div>
        </div>

        <div class="loading" id="loadingIndicator">
            <div class="loading-spinner"></div>
            <p>正在处理请求，请稍候...</p>
        </div>

        <div class="result-container" id="resultContainer" style="display: none;">
            <h2>生成结果</h2>
            <div id="results"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('promptForm');
            const generateBtn = document.getElementById('generateBtn');
            const testApiBtn = document.getElementById('testApiBtn');
            const promptInput = document.getElementById('prompt');
            const results = document.getElementById('results');
            const resultContainer = document.getElementById('resultContainer');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const statusMessage = document.getElementById('statusMessage');
            const apiInfo = document.getElementById('apiInfo');
            const exportBtn = document.getElementById('exportBtn');
            let speechTableGenerated = false;

            // 新增：下一步按钮逻辑
            const nextStepBtn = document.getElementById('nextStepBtn'); // 定义 nextStepBtn
            if (nextStepBtn) { // 检查元素是否存在
                nextStepBtn.addEventListener('click', function() {
                    if (speechTableGenerated) return;
                    nextStepBtn.disabled = true;
                    nextStepBtn.innerHTML = '<span class="loading-dot"></span> 正在生成配音表...';
                    // 模拟异步生成配音表（实际应调用后端或已有逻辑）
                    setTimeout(function() {
                        // 这里假设配音表格HTML已由后端返回，直接插入演示
                        const fakeTable = '<table class="table-auto w-full border border-gray-300 mt-4"><thead><tr class="bg-gray-100"><th class="border px-2 py-1">序号</th><th class="border px-2 py-1">时间</th><th class="border px-2 py-1">角色说话人</th><th class="border px-2 py-1">说话内容</th><th class="border px-2 py-1">配音要求</th></tr></thead><tbody><tr><td>1</td><td>00:00:00,000 --> 00:00:03,000</td><td>旁白</td><td>欢迎来到故事世界！</td><td>温柔女声</td></tr></tbody></table>';
                        // 防止插入全局标签，简单过滤
                        const safeTable = fakeTable.replace(/<(\/)?(html|head|body)[^>]*>/gi, '');
                        results.innerHTML = safeTable;
                        resultContainer.style.display = 'block';
                        nextStepBtn.style.display = 'none';
                        speechTableGenerated = true;
                    }, 1200);
                });
            }

            // 生成绘本
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                console.log('生成按钮被点击');

                const prompt = promptInput.innerHTML.trim();
                if (!prompt) {
                    console.log('提示词为空，终止请求');
                    return;
                }

                // 重置UI
                results.innerHTML = '';
                resultContainer.style.display = 'none';
                statusMessage.className = 'status';
                statusMessage.style.display = 'none';
                loadingIndicator.style.display = 'block';
                generateBtn.disabled = true;

                try {
                    // 获取选中的模型
                    const selectedModel = document.querySelector('input[name="model"]:checked').value;
                    console.log('发送提示词:', prompt, '模型:', selectedModel);
                    const jsonData = JSON.stringify({ prompt: prompt, model: selectedModel });
                    console.log('JSON数据:', jsonData);

                    console.log('开始发送网络请求...');
                    const response = await fetch('/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: jsonData
                    });
                    console.log('网络请求已发送，状态码:', response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }

                    let data;
                    try {
                        data = await response.json();
                        console.log('收到API响应:', data);
                    } catch (jsonError) {
                        console.error('JSON解析错误:', jsonError);
                        throw new Error('无法将响应解析为JSON');
                    }

                    // 显示结果
                    resultContainer.style.display = 'block';

                    // 处理不同的状态类型
                    if (data.status === 'success' || data.status === 'warning') {
                        if (data.status === 'success') {
                            statusMessage.className = 'status success';
                            statusMessage.textContent = '生成成功！';
                        } else {
                            statusMessage.className = 'status warning';
                            statusMessage.textContent = `警告: ${data.message || '部分内容可能未正确生成'}`;
                        }
                        statusMessage.style.display = 'block';

                        // 处理API密钥信息
                        if (data.api_key) {
                            apiInfo.textContent = `使用API密钥: ${data.api_key}`;
                        }

                        // 处理结果
                        if (data.results && data.results.length > 0) {
                            data.results.forEach((item, index) => {
                                console.log(`处理结果项 ${index}:`, item);

                                // 为此结果创建容器
                                const resultItem = document.createElement('div');
                                resultItem.className = 'result-item';

                                // 处理图像
                                if (item.type === 'image' && item.data) {
                                    const imageContainer = document.createElement('div');
                                    imageContainer.className = 'image-container';

                                    try {
                                        const img = document.createElement('img');
                                        img.src = item.data;
                                        img.alt = `生成的图片 #${index+1}`;
                                        img.className = 'generated-image';
                                        img.onerror = function() {
                                            imageContainer.innerHTML = '<p class="error-message">图片加载失败</p>';
                                        };
                                        imageContainer.appendChild(img);
                                    } catch (imgError) {
                                        console.error('图片加载错误:', imgError);
                                        imageContainer.innerHTML = '<p class="error-message">图片处理失败</p>';
                                    }

                                    resultItem.appendChild(imageContainer);
                                }

                                // 处理文本
                                if (item.type === 'text' && item.content) {
                                    // 富文本编辑框实现
                                    const richEditorDiv = document.createElement('div');
                                    richEditorDiv.className = 'rich-html-editor';
                                    richEditorDiv.contentEditable = true;
                                    richEditorDiv.style.minHeight = '120px';
                                    richEditorDiv.style.border = '1px solid #ccc';
                                    richEditorDiv.style.padding = '8px';
                                    richEditorDiv.style.borderRadius = '4px';
                                    richEditorDiv.style.background = '#fff';
                                    richEditorDiv.style.outline = 'none';
                                    richEditorDiv.innerHTML = item.content;
                                    // 可选：监听内容变化，后续可扩展保存/提交逻辑
                                    resultItem.appendChild(richEditorDiv);
                                    if (item.table_html) {
                                        const tableDiv = document.createElement('div');
                                        tableDiv.className = 'table-container';
                                        tableDiv.innerHTML = item.table_html;
                                        resultItem.appendChild(tableDiv);
                                    }
                                    results.appendChild(resultItem);

                                    // 如果标记为只显示一次内容，设置标志防止后续重复显示
                                    if (item.display_content_once) {
                                        item.content_displayed = true;
                                    }
                                }
                                const textContainer = document.createElement('div');
                                textContainer.className = 'text-container';

                                // 添加元数据显示
                                if (item.metadata) {
                                    // 创建元数据容器
                                    const metadataContainer = document.createElement('div');
                                    metadataContainer.className = 'message-metadata';

                                    // 添加角色标识
                                    if (item.metadata.role) {
                                        const roleSpan = document.createElement('span');
                                        roleSpan.className = `role-badge role-${item.metadata.role}`;
                                        roleSpan.textContent = item.metadata.role === 'assistant' ? 'AI' : item.metadata.role;
                                        metadataContainer.appendChild(roleSpan);
                                    }

                                    // 添加序列号
                                    if (item.metadata.sequence) {
                                        const sequenceSpan = document.createElement('span');
                                        sequenceSpan.className = 'sequence-number';
                                        sequenceSpan.textContent = `#${item.metadata.sequence}`;
                                        metadataContainer.appendChild(sequenceSpan);
                                    }

                                    // 添加时间戳
                                    if (item.metadata.timestamp) {
                                        const timeSpan = document.createElement('span');
                                        timeSpan.className = 'timestamp';
                                        timeSpan.textContent = item.metadata.timestamp;
                                        metadataContainer.appendChild(timeSpan);
                                    }

                                    // 如果是对话内容，添加对话标记
                                    if (item.metadata.is_dialogue) {
                                        textContainer.classList.add('dialogue-content');

                                        const dialogueIcon = document.createElement('span');
                                        dialogueIcon.className = 'content-type-icon dialogue-icon';
                                        dialogueIcon.title = '包含对话内容';
                                        dialogueIcon.innerHTML = '<i class="icon">💬</i>';
                                        metadataContainer.appendChild(dialogueIcon);
                                    }

                                    // 添加内容类型指示器
                                    if (item.metadata.content_type) {
                                        const contentTypeContainer = document.createElement('div');
                                        contentTypeContainer.className = 'content-type-container';

                                        if (item.metadata.content_type.has_story) {
                                            const storyIcon = document.createElement('span');
                                            storyIcon.className = 'content-type-icon story-icon';
                                            storyIcon.title = '包含故事内容';
                                            storyIcon.innerHTML = '<i class="icon">📖</i>';
                                            contentTypeContainer.appendChild(storyIcon);
                                        }

                                        if (item.metadata.content_type.has_prompt) {
                                            const promptIcon = document.createElement('span');
                                            promptIcon.className = 'content-type-icon prompt-icon';
                                            promptIcon.title = '包含生图提示词';
                                            promptIcon.innerHTML = '<i class="icon">🎨</i>';
                                            contentTypeContainer.appendChild(promptIcon);
                                        }

                                        if (contentTypeContainer.children.length > 0) {
                                            metadataContainer.appendChild(contentTypeContainer);
                                        }
                                    }

                                    // 添加情感标签
                                    if (item.metadata.emotions && item.metadata.emotions.length > 0) {
                                        const emotionsContainer = document.createElement('div');
                                        emotionsContainer.className = 'emotions-container';

                                        item.metadata.emotions.forEach(emotion => {
                                            const emotionTag = document.createElement('span');
                                            emotionTag.className = `emotion-tag emotion-${emotion}`;
                                            emotionTag.textContent = emotion;
                                            emotionsContainer.appendChild(emotionTag);
                                        });

                                        metadataContainer.appendChild(emotionsContainer);
                                    }

                                    // 添加主题标签
                                    if (item.metadata.themes && item.metadata.themes.length > 0) {
                                        const themesContainer = document.createElement('div');
                                        themesContainer.className = 'themes-container';

                                        item.metadata.themes.forEach(theme => {
                                            const themeTag = document.createElement('span');
                                            themeTag.className = 'theme-tag';
                                            themeTag.textContent = theme;
                                            themesContainer.appendChild(themeTag);
                                        });

                                        metadataContainer.appendChild(themesContainer);
                                    }

                                    // 添加统计信息按钮
                                    if (item.metadata.statistics) {
                                        const statsButton = document.createElement('button');
                                        statsButton.className = 'stats-button';
                                        statsButton.textContent = '统计';
                                        statsButton.title = '显示文本统计信息';

                                        statsButton.addEventListener('click', function() {
                                            const stats = item.metadata.statistics;
                                            alert(`文本统计信息:\n字符数: ${stats.char_count}\n词数: ${stats.word_count}\n句子数: ${stats.sentence_count}`);
                                        });

                                        metadataContainer.appendChild(statsButton);
                                    }

                                    // 添加原始文本查看按钮
                                    if (item.raw_text) {
                                        const rawTextButton = document.createElement('button');
                                        rawTextButton.className = 'raw-text-button';
                                        rawTextButton.textContent = '原文';
                                        rawTextButton.title = '查看原始未格式化文本';

                                        rawTextButton.addEventListener('click', function() {
                                            // 创建模态框显示原始文本
                                            const modal = document.createElement('div');
                                            modal.className = 'modal';

                                            const modalContent = document.createElement('div');
                                            modalContent.className = 'modal-content';

                                            const closeBtn = document.createElement('span');
                                            closeBtn.className = 'close-button';
                                            closeBtn.innerHTML = '&times;';
                                            closeBtn.onclick = function() {
                                                document.body.removeChild(modal);
                                            };

                                            const title = document.createElement('h3');
                                            title.textContent = '原始未格式化文本';

                                            const content = document.createElement('pre');
                                            content.className = 'raw-text';
                                            content.textContent = item.raw_text;

                                            modalContent.appendChild(closeBtn);
                                            modalContent.appendChild(title);
                                            modalContent.appendChild(content);
                                            modal.appendChild(modalContent);

                                            // 点击模态框外部关闭
                                            modal.onclick = function(event) {
                                                if (event.target === modal) {
                                                    document.body.removeChild(modal);
                                                }
                                            };

                                            document.body.appendChild(modal);
                                        });

                                        metadataContainer.appendChild(rawTextButton);
                                    }

                                    textContainer.appendChild(metadataContainer);
                                }

                                // 创建内容容器
                                const contentContainer = document.createElement('div');
                                contentContainer.className = 'message-content';

                                // 文本内容可能已经包含HTML标签，直接使用innerHTML显示
                                // 由于后端已经进行了文本美化和HTML处理，这里直接显示
                                // 检查内容是否已经显示过，避免重复显示
                                if (item.content && (!item.display_content_once || !item.content_displayed)) {
                                    contentContainer.innerHTML = item.content;

                                    // 为没有段落标签的文本添加段落标签
                                    if (!item.content.includes('<p>')) {
                                        const textElement = document.createElement('div');
                                        textElement.textContent = item.content;
                                        const formattedText = textElement.textContent.replace(/\n/g, '<br>');
                                        contentContainer.innerHTML = `<p>${formattedText}</p>`;
                                    }

                                    // 标记内容已显示
                                    if (item.display_content_once) {
                                        item.content_displayed = true;
                                    }
                                }

                                textContainer.appendChild(contentContainer);
                                // 确保文本容器被添加到结果项中
                                if (!resultItem.querySelector('.text-container')) {
                                    resultItem.appendChild(textContainer);
                                }
                                // 确保结果项被添加到结果容器中
                                if (!results.contains(resultItem)) {
                                    results.appendChild(resultItem);
                                }
                            });
                        } else {
                            results.innerHTML = '<p>未返回任何结果。</p>';
                        }
                    } else {
                        // 处理错误状态
                        statusMessage.className = 'status error';
                        statusMessage.textContent = data.message || '生成过程中发生错误';
                        statusMessage.style.display = 'block';

                        if (data.error_details) {
                            const errorDetails = document.createElement('div');
                            errorDetails.className = 'error-details';
                            errorDetails.textContent = data.error_details;
                            results.appendChild(errorDetails);
                        }
                    }
                } catch (error) {
                    console.error('请求错误:', error);
                    statusMessage.className = 'status error';
                    statusMessage.textContent = error.message || '无法与服务器通信';
                    statusMessage.style.display = 'block';
                } finally {
                    loadingIndicator.style.display = 'none';
                    generateBtn.disabled = false;
                }
            });

            // 测试API连接
            testApiBtn.addEventListener('click', async function() {
                console.log('测试API按钮被点击');
                testApiBtn.disabled = true;
                apiInfo.textContent = '正在测试API连接...';

                try {
                    console.log('开始发送测试API请求...');
                    const response = await fetch('/test_api', { method: 'GET' });
                    console.log('测试API请求已发送，状态码:', response.status);
                    const data = await response.json();
                    console.log('测试API响应数据:', data);

                    if (data.status === 'success') {
                        apiInfo.textContent = `API连接成功! 可用模型: ${data.models.join(', ')}`;
                    } else {
                        apiInfo.textContent = `API测试失败: ${data.message}`;
                    }
                } catch (error) {
                    console.error('测试API请求错误:', error);
                    apiInfo.textContent = '测试API连接时出错';
                } finally {
                    testApiBtn.disabled = false;
                }
            });

            // 导出故事按钮逻辑
            exportBtn.addEventListener('click', async function() {
                console.log('导出故事按钮被点击');
                // 获取当前生成的内容
                const storyTitle = (promptInput.textContent || promptInput.innerText || "").trim().split('\n')[0] || '故事';
                const defaultFileName = storyTitle.replace(/[^\u4e00-\u9fa5\w]/g, '') + '.docx';
                // 收集所有生成的文本和图片
                const resultItems = [];
                document.querySelectorAll('#results .result-item').forEach(item => {
                    // 文本
                    const textDiv = item.querySelector('.text-container');
                    if (textDiv) {
                        // 去除HTML标签，保留纯文本
                        const text = textDiv.textContent || '';
                        if (text.trim()) {
                            resultItems.push({type: 'text', content: text.trim()});
                        }
                    }
                    // 图片
                    const img = item.querySelector('img');
                    if (img && img.src && img.src.startsWith('data:image/')) {
                        resultItems.push({type: 'image', data: img.src});
                    }
                });
                if (resultItems.length === 0) {
                    alert('没有可导出的内容，请先生成故事！');
                    return;
                }
                exportBtn.disabled = true;
                exportBtn.textContent = '正在导出...';
                try {
                    const response = await fetch('/export', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ title: storyTitle, results: resultItems })
                    });
                    if (!response.ok) {
                        throw new Error('导出失败: ' + response.status);
                    }
                    const blob = await response.blob();
                    // 处理后端返回的错误信息
                    if (blob.type.indexOf('application/json') !== -1) {
                        const reader = new FileReader();
                        reader.onload = function() {
                            const err = JSON.parse(reader.result);
                            alert('导出失败: ' + (err.message || '未知错误'));
                        };
                        reader.readAsText(blob);
                        return;
                    }
                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = defaultFileName;
                    document.body.appendChild(a);
                    a.click();
                    setTimeout(() => {
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                    }, 100);
                } catch (err) {
                    alert('导出失败: ' + (err.message || err));
                } finally {
                    exportBtn.disabled = false;
                    exportBtn.textContent = '导出故事';
                }
            });
        });
    </script>
</body>
</html>