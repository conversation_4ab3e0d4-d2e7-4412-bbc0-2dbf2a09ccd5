import base64
import os
import random
import time
import logging
import pangu  # 新增：引入pangu库
import sys
from datetime import datetime
import traceback
import uuid
import re
from flask import Flask, render_template, request, jsonify, send_from_directory, send_file
from google import genai
from google.genai import types
from config import API_KEYS
import io
from generate_speech_table import generate_speech_table
try:
    from docx import Document
    from docx.shared import Inches
except ImportError:
    Document = None  # Will error if not installed

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler('app.log')
    ]
)

# Keep track of used API keys for cycling
current_key_index = 0
INVALID_API_KEYS = set()

# Create Flask app
app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB file size limit
app.config['UPLOAD_FOLDER'] = 'temp'

# Ensure temp directory exists
temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
if not os.path.exists(temp_dir):
    os.makedirs(temp_dir)
    logging.info(f"Created temp directory: {temp_dir}")

def get_next_api_key():
    """Get the next API key using cycling strategy"""
    global current_key_index
    valid_keys = [key for key in API_KEYS if key not in INVALID_API_KEYS]
    print(f"[步骤] 当前有效API KEY数量: {len(valid_keys)}，已失效KEY数量: {len(INVALID_API_KEYS)}")
    if not valid_keys:
        logging.error("No valid API keys available")
        print("[错误] 没有可用的API KEY！")
        raise ValueError("No valid API keys available")
    # Select initial key randomly if it's the first use
    if current_key_index == 0:
        current_key_index = random.randint(0, len(valid_keys) - 1)
        print(f"[步骤] 首次使用，随机选择KEY索引: {current_key_index}")
    else:
        # Move to next key
        current_key_index = (current_key_index + 1) % len(valid_keys)
        print(f"[步骤] 轮换到下一个KEY索引: {current_key_index}")
    selected_key = valid_keys[current_key_index]
    print(f"[信息] 当前使用API KEY前10位: {selected_key[:10]}")
    return selected_key

def save_binary_file(file_name, data):
    """Save binary data to a file"""
    print(f"[步骤] 保存二进制文件: {file_name}")
    try:
        with open(file_name, "wb") as f:
            f.write(data)
        print(f"[成功] 文件保存成功: {file_name}")
        return True
    except Exception as e:
        logging.error(f"Error saving file {file_name}: {e}")
        print(f"[错误] 文件保存失败: {file_name}, 错误: {e}")
        return False

def beautify_text(text):
    """美化文本，去除多余换行，对章节进行合理分段，保持文本连贯性

    根据用户需求优化文本排版，确保特殊标记（如标题、场景描述等）保持在同一行，
    减少不必要的换行，使文本更加连贯易读。同时保留HTML标签。

    Args:
        text (str): 需要美化的原始文本

    Returns:
        str: 美化后的文本
    """
    if not text or not isinstance(text, str):
        return text

    # 保存HTML标签
    html_tags = []
    def save_html_tag(match):
        html_tags.append(match.group(0))
        return f"__HTML_TAG_{len(html_tags)-1}__"

    # 临时保存HTML标签
    text_with_placeholders = re.sub(r'<[^>]+>', save_html_tag, text)

    # 去除多余换行，保留段落分隔
    lines = text_with_placeholders.split('\n')
    processed_lines = []
    current_paragraph = []

    # 识别章节标记和特殊标题
    section_markers = [
        '【生图提示词】', '【旁白', '【旁白配音】', '【',
        '生图提示词：', '旁白：', '旁白配音：',
        '主题：', '故事：', '对白：', '场景配图生图prompt:',
        '第一页', '第二页', '第三页', '第四页', '第五页'
    ]

    # 标题和内容标记，这些标记后的内容应该与标记保持在同一行
    title_markers = ['主题：', '故事：', '对白：', '场景配图生图prompt:']

    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            # 空行作为段落分隔符
            if current_paragraph:
                processed_lines.append(' '.join(current_paragraph))
                current_paragraph = []
        elif any(marker in line for marker in section_markers):
            # 章节标记作为新段落的开始
            if current_paragraph:
                processed_lines.append(' '.join(current_paragraph))

            # 检查是否是标题标记，如果是，尝试将下一行内容合并到当前行
            if any(marker in line for marker in title_markers) and i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line and not any(marker in next_line for marker in section_markers):
                    # 将标题和内容合并为一行
                    current_paragraph = [line + ' ' + next_line]
                    # 跳过下一行，因为已经处理过了
                    lines[i + 1] = ''
                else:
                    current_paragraph = [line]
            else:
                current_paragraph = [line]
        else:
            # 如果当前行不是空行且不包含章节标记，则添加到当前段落
            if current_paragraph and not any(marker in current_paragraph[0] for marker in title_markers):
                current_paragraph.append(line)
            else:
                # 如果当前没有段落或当前段落是标题，则创建新段落
                if current_paragraph:
                    processed_lines.append(' '.join(current_paragraph))
                current_paragraph = [line]

    # 添加最后一个段落
    if current_paragraph:
        processed_lines.append(' '.join(current_paragraph))

    # 合并处理后的段落，使用HTML段落标签
    result = '<p>' + '</p><p>'.join(processed_lines) + '</p>'

    # 恢复HTML标签
    for i, tag in enumerate(html_tags):
        result = result.replace(f"__HTML_TAG_{i}__", tag)

    return result

def generate_with_key(
    prompt: str,
    api_key: str,
    model: str = "gemini-2.0-flash",
    max_retries: int = 3
) -> dict:
    """
    使用指定API key生成内容，支持重试机制

    参数:
        prompt (str): 用户输入的提示词
        api_key (str): Google Gemini API密钥
        model (str): 使用的模型名称
        max_retries (int): 最大重试次数

    返回:
        dict: 包含生成结果或错误信息的字典
    """
    retry_count = 0
    error_msg = "未知错误"
    print(f"[步骤] 开始调用generate_with_key，模型: {model}，最大重试: {max_retries}")
    while retry_count < max_retries:
        try:
            print(f"[步骤] 第{retry_count+1}次尝试，使用API KEY前10位: {api_key[:10]}")
            client = genai.Client(api_key=api_key)
            # 拼接前缀提示词
            prefix = "请根据中文排版规范，合理美化分段，并用HTML格式，包含tailwindCSS方式完善美化后输出以下完整内容："
            merged_prompt = prefix + "\n" + prompt

            contents = [
                types.Content(
                    role="user",
                    parts=[types.Part.from_text(text="我们开始新的对话！")],
                ),
                types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=merged_prompt)],
                ),
            ]

            # 根据不同模型使用不同的配置
            if "flash-exp-image-generation" in model:
                generate_content_config = types.GenerateContentConfig(
                    temperature=1,
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=8192,
                    response_modalities=["image", "text"],
                    response_mime_type="text/plain",
                )
            else:
                generate_content_config = types.GenerateContentConfig(
                    temperature=1,
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=8192,
                )

            print("[步骤] 开始调用Gemini模型API...")
            response = client.models.generate_content(
                model=model,
                contents=contents,
                config=generate_content_config,
            )

            print("[成功] Gemini模型API调用完成，开始处理返回内容...")
            results = []
            # 合并所有文本内容
            full_text = ""
            if response.candidates and response.candidates[0].content and response.candidates[0].content.parts:
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'inline_data') and part.inline_data:
                        image_data = part.inline_data.data
                        image_mime = part.inline_data.mime_type
                        image_base64 = base64.b64encode(image_data).decode('utf-8')
                        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                        unique_id = uuid.uuid4().hex[:8]
                        file_name = os.path.join(temp_dir, f"gemini_image_{timestamp}_{unique_id}.png")
                        save_binary_file(file_name, image_data)
                        results.append({
                            "type": "image",
                            "data": f"data:{image_mime};base64,{image_base64}"
                        })
                        print(f"[信息] 已处理图片内容并保存: {file_name}")
                    elif hasattr(part, 'text') and part.text and part.text.strip():
                        full_text += part.text
            if full_text:
                print("[信息] 已获取文本内容，准备生成表格...")
                table_html = generate_speech_table(full_text)
                # 只发送一次内容，避免前端重复显示
                results.append({
                    "type": "text",
                    "content": full_text,
                    "table_html": table_html,
                    "display_content_once": True  # 添加标记，指示前端只显示一次内容
                })
                # 注意：不再重复插入LLM原始HTML内容，避免重复显示
            print("[步骤] 内容处理完成，返回结果")
            return {
                "status": "success",
                "results": results,
                "api_key": f"{api_key[:10]}..."
            }
        except Exception as e:
            retry_count += 1
            error_msg = str(e)
            print(f"[错误] Gemini API调用异常: {error_msg}，重试次数: {retry_count}")
            if "API key not valid" in error_msg:
                INVALID_API_KEYS.add(api_key)
                break
            elif retry_count < max_retries:
                time.sleep(1)

    print(f"[失败] 所有尝试失败，共重试{retry_count}次，错误信息: {error_msg}")
    return {
        "status": "error",
        "message": f"Failed after {retry_count} attempts: {error_msg}",
        "api_key": f"{api_key[:10]}..."
    }

def generate(model="gemini-2.0-flash-exp-image-generation"):
    """Generate content using API key rotation"""
    max_keys_to_try = len(API_KEYS) - len(INVALID_API_KEYS)
    keys_tried = 0
    prompt = """
        请以皮克斯3D立体卡通动画风格生成一个关于《灰姑娘》的2页绘本故事。
        对于每页绘本的场景，生成一个含有人物和情节的图像。
        对于每页绘本场景输出生图Prompt展示（Stable diffusion XL格式），在【生图提示词】后输出。
        对于每页绘本场景有详细的旁白，在【旁白】后输出。
        对于每页绘本场景有角色详细对白，在【X角色】后输出。
        注意：
        1、直接输出文本。
        2、请注意保持角色的前后一致性，每页绘本场景的图片有相关的角色出现，
        都必须参考前面图片的角色的外貌和形体特征。
    """

    while keys_tried < max_keys_to_try:
        try:
            api_key = get_next_api_key()
            result = generate_with_key(prompt, api_key, model)

            if result["status"] == "success":
                return result

            # If we got an error but not due to invalid key, return the error
            if "API key not valid" not in result.get("message", ""):
                return result

            keys_tried += 1

        except Exception as e:
            return {
                "status": "error",
                "message": f"Key rotation error: {str(e)}"
            }

    return {
        "status": "error",
        "message": f"All available keys failed ({keys_tried} keys tried)"
    }

@app.route('/')
def index():
    """Render main page"""
    api_key = get_next_api_key()
    default_prompt = """
    请以皮克斯3D立体卡通风格生成一个关于安徒生童话《丑小鸭》的2页中文绘本故事。
    对于每页绘本的场景，生成一个含有人物和情节的图像。
    对于每页绘本场景输出生图Prompt展示（Stable diffusion XL格式英文版，加上"安徒生童话《丑小鸭》的故事"英文翻译），在【生图提示词】：后输出并换行。
    对于每页绘本场景有详细的旁白，在【旁白配音】：（对旁白配音要求年龄，男女声，特色）后输出并换行。
    对于每页绘本场景有角色详细对白，在【XX角色配音】：（对角色配音要求，年龄，男女声，特色）后输出并换行。
    注意：
    1、不用回答任何内容，直接输出图文消息。文字按章节整段不换行输出。
    2、请注意保持角色的前后一致性，每页绘本场景的图片有相关的角色出现，都必须参考前面图片的角色的外貌和形体特征。
"""

    logging.info("Rendering index page")
    return render_template('index.html', api_key=f"{api_key[:10]}...", default_prompt=default_prompt)

@app.route('/generate', methods=['POST'])
def api_generate():
    """API endpoint for generation"""
    try:
        data = request.json
        if not data:
            return jsonify({'status': 'error', 'message': 'No data provided'})

        prompt = data.get('prompt', '')
        if not prompt:
            return jsonify({'status': 'error', 'message': 'No prompt provided'})

        model = data.get('model', 'gemini-2.0-flash')  # 默认模型改为gemini-2.0-flash

        max_keys_to_try = len(API_KEYS) - len(INVALID_API_KEYS)
        keys_tried = 0

        while keys_tried < max_keys_to_try:
            try:
                api_key = get_next_api_key()
                result = generate_with_key(prompt, api_key, model)

                if result["status"] == "success":
                    return jsonify(result)

                # If we got an error but not due to invalid key, return the error
                if "API key not valid" not in result.get("message", ""):
                    return jsonify(result)

                keys_tried += 1

            except Exception as e:
                error_msg = str(e)
                return jsonify({
                    'status': 'error',
                    'message': f'Generation failed: {error_msg}'
                })

        return jsonify({
            'status': 'error',
            'message': f'All available keys failed ({keys_tried} keys tried)'
        })
    except Exception as e:
        error_msg = str(e)
        return jsonify({
            'status': 'error',
            'message': f'Unexpected error: {error_msg}'
        })

@app.route('/test_api', methods=['GET'])
def test_api():
    """Test API connection"""
    try:
        model = request.args.get('model', 'gemini-2.0-flash')  # 支持前端传递模型
        api_key = get_next_api_key()
        if not api_key:
            return jsonify({
                'status': 'error',
                'message': 'No valid API key available'
            })

        client = genai.Client(api_key=api_key)

        # 实际可根据模型做更细致的测试
        models = [model]

        return jsonify({
            'status': 'success',
            'models': models
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'API test failed: {str(e)}'
        })

@app.route('/temp/<filename>')
def serve_file(filename):
    """Serve temporary files"""
    return send_from_directory(temp_dir, filename)

@app.route('/export', methods=['POST'])
def export_story():
    """Export the story to a docx file and return for download"""
    if Document is None:
        return jsonify({'status': 'error', 'message': '服务器未安装python-docx库，请先安装 python-docx'}), 500
    data = request.get_json()
    if not data:
        return jsonify({'status': 'error', 'message': '未收到导出数据'}), 400
    title = data.get('title', '故事')
    results = data.get('results', [])
    doc = Document()
    doc.add_heading(title, 0)
    for idx, item in enumerate(results):
        if item.get('type') == 'text' and item.get('content'):
            doc.add_paragraph(item['content'])
        elif item.get('type') == 'image' and item.get('data'):
            # 处理base64图片
            try:
                import re, base64
                img_data = item['data']
                match = re.match(r'data:(image/\w+);base64,(.+)', img_data)
                if match:
                    img_bytes = base64.b64decode(match.group(2))
                    image_stream = io.BytesIO(img_bytes)
                    doc.add_picture(image_stream, width=Inches(4.5))
            except Exception as e:
                doc.add_paragraph(f'[图片插入失败: {e}]')
        doc.add_paragraph('')  # 空行分隔
    # 保存到内存
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    safe_title = ''.join([c for c in title if c.isalnum() or '\u4e00' <= c <= '\u9fa5']) or '故事'
    filename = f"{safe_title}.docx"
    return send_file(file_stream, as_attachment=True, download_name=filename, mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document')

def generate_speech_table(html_text: str) -> str:
    """
    解析LLM返回的HTML文本，生成带有SRT时间区间的说话表格

    参数:
        html_text (str): LLM返回的HTML文本

    返回:
        str: 包含表格的HTML字符串
    """
    import re
    from bs4 import BeautifulSoup

    soup = BeautifulSoup(html_text, "html.parser")
    rows = []
    # 匹配【旁白配音】：内容（配音要求） 或 【角色配音】：内容（配音要求） 或 【角色】：内容
    pattern = re.compile(
        r"(?:【)?([\u4e00-\u9fa5A-Za-z0-9_]+?)(配音)?[：:】]\s*([^\(（]+?)(?:[\(（]([^\)）]+)[\)）])?$"
    )
    for p in soup.find_all("p"):
        text = p.get_text(strip=True)
        match = pattern.match(text)
        if match:
            speaker = match.group(1)
            is_voice = match.group(2)
            content = match.group(3).strip()
            voice_req = match.group(4).strip() if match.group(4) else ""
            # 只保留真正的角色名（不含“配音”二字的说话人）
            if is_voice:
                continue  # 跳过所有“配音”相关的说话人
            rows.append((speaker, content, voice_req))

    # 计算每段的时间区间
    table_rows = []
    current_time = 0.0
    for idx, (speaker, content, voice_req) in enumerate(rows, 1):
        duration = len(content) / 2.0
        start_time = current_time
        end_time = start_time + duration
        start_time = max(0, start_time - 1)
        end_time += 1
        def format_srt_time(t):
            h = int(t // 3600)
            m = int((t % 3600) // 60)
            s = int(t % 60)
            ms = int((t - int(t)) * 1000)
            return f"{h:02}:{m:02}:{s:02},{ms:03}"
        time_range = f"{format_srt_time(start_time)} --> {format_srt_time(end_time)}"
        table_rows.append(
            f"<tr><td>{idx}</td><td>{time_range}</td><td>{speaker}</td><td>{content}</td><td>{voice_req}</td></tr>"
        )
        current_time = end_time

    table_html = """
    <table class="table-auto w-full border border-gray-300 mt-4">
        <thead>
            <tr class="bg-gray-100">
                <th class="border px-2 py-1">序号</th>
                <th class="border px-2 py-1">时间</th>
                <th class="border px-2 py-1">角色说话人</th>
                <th class="border px-2 py-1">说话内容</th>
                <th class="border px-2 py-1">配音要求</th>
            </tr>
        </thead>
        <tbody>
            %s
        </tbody>
    </table>
    """ % "\n".join(table_rows)
    return table_html

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "cli":
        # Run in command line mode
        result = generate()
        if result["status"] == "success":
            print(f"Generation successful using key {result['api_key']}")
            for item in result["results"]:
                if item["type"] == "text":
                    print(item["content"])
                else:
                    print(f"[Image generated]")
        else:
            print(f"Generation failed: {result['message']}")
    else:
        # Run as web server
        host = "************"
        port = 5000
        print(f"Starting web server on http://{host}:{port}")
        app.run(debug=True, host=host, port=port)

