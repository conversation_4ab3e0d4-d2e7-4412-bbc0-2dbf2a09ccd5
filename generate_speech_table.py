def generate_speech_table(html_text: str) -> str:
    """
    解析LLM返回的HTML文本，生成带有SRT时间区间的说话表格
    
    规则：
    1. "页面详细概述"和"页面概述配音要求"成对出现，"页面概述配音要求"的内容填入列5
    2. "XX角色详细对白"和"XX角色配音要求"成对出现，"XX角色配音要求"的内容填入列5，对应的说话内容填写到列4

    参数:
        html_text (str): LLM返回的HTML文本

    返回:
        str: 包含表格的HTML字符串
    """
    import re
    from bs4 import BeautifulSoup

    soup = BeautifulSoup(html_text, "html.parser")
    
    # 提取所有段落文本
    paragraphs = []
    for p in soup.find_all("p"):
        text = p.get_text(strip=True)
        if text:
            paragraphs.append(text)
    
    # 处理配音表格数据
    table_data = []
    i = 0
    
    while i < len(paragraphs):
        current_text = paragraphs[i]
        
        # 匹配页面详细概述
        page_overview_match = re.search(r'(?:【)?(页面详细概述)[：:】]?\s*(.*)', current_text)
        if page_overview_match and i + 1 < len(paragraphs):
            overview_content = page_overview_match.group(2).strip()
            next_text = paragraphs[i + 1]
            
            # 检查下一段是否是页面概述配音要求
            voice_req_match = re.search(r'(?:【)?(页面概述配音要求)[：:】]?\s*(.*)', next_text)
            if voice_req_match:
                voice_req = voice_req_match.group(2).strip()
                # 添加到表格数据
                table_data.append(("页面概述", overview_content, voice_req))
                i += 2  # 跳过这两行
                continue
        
        # 匹配角色详细对白
        character_dialogue_match = re.search(r'(?:【)?([^【】]+?)(角色详细对白)[：:】]?\s*(.*)', current_text)
        if character_dialogue_match and i + 1 < len(paragraphs):
            character = character_dialogue_match.group(1).strip()
            dialogue = character_dialogue_match.group(3).strip()
            next_text = paragraphs[i + 1]
            
            # 检查下一段是否是角色配音要求
            voice_req_match = re.search(r'(?:【)?([^【】]+?)(角色配音要求)[：:】]?\s*(.*)', next_text)
            if voice_req_match and voice_req_match.group(1).strip() == character:
                voice_req = voice_req_match.group(3).strip()
                # 添加到表格数据
                table_data.append((character, dialogue, voice_req))
                i += 2  # 跳过这两行
                continue
        
        # 匹配旁白配音和其他角色对白（兼容旧格式）
        general_match = re.search(r'(?:【)?([^【】]+?)(配音)?[：:】]?\s*(.*?)(?:\s*[\(（]([^\)）]+)[\)）])?$', current_text)
        if general_match:
            speaker = general_match.group(1).strip()
            is_voice_req = general_match.group(2) == "配音"  # 是否是配音要求
            content = general_match.group(3).strip()
            voice_req = general_match.group(4).strip() if general_match.group(4) else ""
            
            # 只处理角色对白，不处理配音要求
            if not is_voice_req and speaker not in ["页面详细概述", "页面概述配音要求"]:
                table_data.append((speaker, content, voice_req))
        
        i += 1  # 移动到下一段
    
    # 计算每段的时间区间
    table_rows = []
    current_time = 0.0
    for idx, (speaker, content, voice_req) in enumerate(table_data, 1):
        duration = len(content) / 2.0
        start_time = current_time
        end_time = start_time + duration
        start_time = max(0, start_time - 1)
        end_time += 1
        def format_srt_time(t):
            h = int(t // 3600)
            m = int((t % 3600) // 60)
            s = int(t % 60)
            ms = int((t - int(t)) * 1000)
            return f"{h:02}:{m:02}:{s:02},{ms:03}"
        time_range = f"{format_srt_time(start_time)} --> {format_srt_time(end_time)}"
        table_rows.append(
            f"<tr><td>{idx}</td><td>{time_range}</td><td>{speaker}</td><td>{content}</td><td>{voice_req}</td></tr>"
        )
        current_time = end_time

    table_html = """
    <table class="table-auto w-full border border-gray-300 mt-4">
        <thead>
            <tr class="bg-gray-100">
                <th class="border px-2 py-1">序号</th>
                <th class="border px-2 py-1">时间</th>
                <th class="border px-2 py-1">角色说话人</th>
                <th class="border px-2 py-1">说话内容</th>
                <th class="border px-2 py-1">配音要求</th>
            </tr>
        </thead>
        <tbody>
            %s
        </tbody>
    </table>
    """ % "\n".join(table_rows)
    return table_html
